{"rustc": 8210029788606052455, "features": "[\"std\", \"unicode-bool\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 11077573898738804050], [9408802513701742484, "regex_syntax", false, 3259993366863764573]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-9e287674302c08c3/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}