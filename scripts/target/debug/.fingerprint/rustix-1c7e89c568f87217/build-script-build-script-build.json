{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 5408242616063297496, "profile": 2210360036522109082, "path": 5369205744696581960, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-1c7e89c568f87217/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}