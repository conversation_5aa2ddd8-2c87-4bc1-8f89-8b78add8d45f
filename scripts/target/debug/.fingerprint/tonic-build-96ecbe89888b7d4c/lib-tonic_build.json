{"rustc": 8210029788606052455, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 7401772136729445168, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 6085923160104743134], [3060637413840920116, "proc_macro2", false, 10517735122659648451], [8549471757621926118, "prettyplease", false, 10734107673083840451], [16470553738848018267, "prost_types", false, 7882365844040036096], [17990358020177143287, "quote", false, 3340324781797369755], [18149961000318489080, "syn", false, 5108006150857220384]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-96ecbe89888b7d4c/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}