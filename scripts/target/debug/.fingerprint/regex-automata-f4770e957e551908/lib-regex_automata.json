{"rustc": 8210029788606052455, "features": "[\"alloc\", \"meta\", \"nfa-pikevm\", \"nfa-thompson\", \"std\", \"syntax\", \"unicode-bool\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 5347358027863023418, "path": 2365493837025371978, "deps": [[9408802513701742484, "regex_syntax", false, 3259993366863764573]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-automata-f4770e957e551908/dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}