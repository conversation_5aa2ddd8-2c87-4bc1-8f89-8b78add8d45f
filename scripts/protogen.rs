use std::path::PathBuf;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let proto_file = "../protos/shredstream.proto";
    let proto_dir = "../protos";

    let out_dir = PathBuf::from("../src/generated");
    std::fs::create_dir_all(&out_dir)?;

    tonic_build::configure().out_dir(&out_dir).compile_protos(&[proto_file], &[proto_dir])?;

    println!("Generated protobuf code in: {}", out_dir.display());

    Ok(())
}
