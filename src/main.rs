mod config;
mod core;

use core::{config::load_config, logger::Logger};

fn main() -> anyhow::Result<()> {
    let config = load_config()?;
    let _logger = Logger::init(&config.logger)?;

    tracing::trace!("This is a TRACE message");
    tracing::debug!("This is a DEBUG message");
    tracing::info!("This is an INFO message");
    tracing::warn!("This is a WARN message");
    tracing::error!("This is an ERROR message");

    Ok(())
}
